#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版视频马赛克处理工具 - PaddleOCR版本
功能：
1. 支持多个指定 YOLOv8 .pt 文件进行目标检测
2. 使用PaddleOCR进行精确的敏感信息检测和脱敏（身份证号、姓名、银行卡号等）
3. 实时进度查询接口
4. 处理完成回调机制
5. 增强的日志系统
6. 多种脱敏方式：马赛克、黑条、模糊
7. OCR帧间隔优化，提高视频处理性能
8. 智能敏感信息验证，减少误检

支持的敏感信息类型：
- 身份证号码（15位/18位）- 带智能验证
- 银行卡号码（15位/16位/19位）- 带Luhn算法验证
- 中文姓名 - 带姓氏验证
- 地址数字信息

命令示例：
# 单文件处理 - 仅YOLO检测（输出文件名与输入一致）
python mosaic_video_paddle.py -i input.mp4 -o output_dir --yolo_models models/yolov8n-face.pt

# 单文件处理 - 仅PaddleOCR敏感文字检测（输出文件名与输入一致）
python mosaic_video_paddle.py -i input.mp4 -o output_dir --enable_ocr --ocr_mode all --no_yolo

# 单文件处理 - YOLO + PaddleOCR组合检测（输出文件名与输入一致）
python mosaic_video_paddle.py -i input.mp4 -o output_dir --yolo_models models/yolov8n-face.pt --enable_ocr --ocr_mode all

# 单文件处理 - 指定文字脱敏方式和OCR间隔（输出为 output_dir/input.mp4）
python mosaic_video_paddle.py -i input.mp4 -o output_dir --enable_ocr --text_method black_bar --ocr_interval 15

# 单文件处理 - 强制输出为MP4格式（改变扩展名）
python mosaic_video_paddle.py -i input.avi -o output_dir/result.mp4 --output_format mp4 --enable_ocr

# 批量处理 - 处理整个目录的视频文件（保持原文件名和格式）
python mosaic_video_paddle.py -i input_folder -o output_folder --batch --yolo_models models/yolov8n-face.pt

# 批量处理 - 仅PaddleOCR检测
python mosaic_video_paddle.py -i input_folder -o output_folder --batch --enable_ocr --ocr_mode all --no_yolo

# 批量处理 - YOLO + PaddleOCR组合
python mosaic_video_paddle.py -i input_folder -o output_folder --batch --yolo_models models/yolov8n-face.pt --enable_ocr
"""

import argparse
import logging
import sys
import time
import re
from pathlib import Path
from typing import Optional, Callable, Dict, List, Tuple

import cv2
import numpy as np
from ultralytics import YOLO

# 检查PaddleOCR依赖
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，请运行: pip install paddleocr==2.7.3")

# 确保日志目录存在
log_dir = Path("models")
log_dir.mkdir(exist_ok=True)

# 配置东八区时区
import datetime
import pytz

class BeijingFormatter(logging.Formatter):
    """自定义格式化器，使用北京时间"""
    def __init__(self, fmt=None, datefmt=None):
        super().__init__(fmt, datefmt)
        self.beijing_tz = pytz.timezone('Asia/Shanghai')

    def formatTime(self, record, datefmt=None):
        # 将UTC时间转换为北京时间
        dt = datetime.datetime.fromtimestamp(record.created, tz=self.beijing_tz)
        if datefmt:
            return dt.strftime(datefmt)
        else:
            return dt.strftime('%Y-%m-%d %H:%M:%S')

# 配置日志系统
beijing_formatter = BeijingFormatter('[%(asctime)s] [%(levelname)8s] %(filename)s:%(lineno)d - %(message)s')

# 创建处理器
file_handler = logging.FileHandler(log_dir / 'video_mosaic_paddle.log', encoding='utf-8')
console_handler = logging.StreamHandler(sys.stdout)

# 设置格式化器
file_handler.setFormatter(beijing_formatter)
console_handler.setFormatter(beijing_formatter)

# 配置根日志器
logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, console_handler],
    force=True  # 强制重新配置日志
)
logger = logging.getLogger(__name__)

# 设置日志级别，确保INFO及以上级别的日志都能显示
logger.setLevel(logging.INFO)
for handler in logger.handlers:
    handler.setLevel(logging.INFO)


class VideoProcessorPaddle:
    def __init__(
            self,
            yolo_model_paths: List[str],
            enable_yolo: bool = True,
            enable_ocr: bool = False,
            ocr_mode: str = 'all',
            ocr_frame_interval: int = 30,
            ocr_model_path: str = None,
            output_format: str = 'same'
    ):
        """
        初始化检测器 - PaddleOCR版本
        :param yolo_model_paths: 用户指定的多个 YOLOv8 模型路径列表
        :param enable_yolo: 是否启用 YOLO 检测
        :param enable_ocr: 是否启用 PaddleOCR 文字检测
        :param ocr_mode: OCR 检测模式 ('id_card', 'bank_card', 'driver_license', 'all')
        :param ocr_frame_interval: OCR检测帧间隔（每N帧检测一次，提高性能）
        :param ocr_model_path: PaddleOCR模型路径
        :param output_format: 输出视频格式 ('same'默认-与输入格式一致, 'auto', 'mp4', 'avi', 'mov', 'mkv')
        """
        self._current_frame = 0
        self._total_frames = 0
        self._processing = False
        self._start_time = 0.0

        self.enable_yolo = enable_yolo
        self.enable_ocr = enable_ocr
        self.ocr_mode = ocr_mode
        self.ocr_frame_interval = ocr_frame_interval
        self.output_format = output_format
        self.yolo_models = []
        self.yolo_classes = {}  # 存储每个模型的类别
        self.ocr_engine = None
        self._last_ocr_results = []  # 缓存OCR结果

        # 敏感信息正则表达式模式 - 优化版本，参考mosaic_image_paddle.py
        self.sensitive_patterns = {
            'id_card': [
                # 标准18位身份证号（最后一位可能是X）
                r'\b\d{17}[\dXx]\b',
                # 15位身份证号
                r'\b\d{15}\b',
                # 容错模式：17位数字+X/x
                r'\b\d{17}[Xx]\b',
                # 容错模式：包含空格或分隔符的身份证号
                r'\b\d{6}[\s\-]?\d{8}[\s\-]?\d{3}[\dXx]\b',
                r'\b\d{6}[\s\-]?\d{4}[\s\-]?\d{2}[\s\-]?\d{2}[\s\-]?\d{3}[\dXx]\b',
                # 宽松模式：14-18位数字，可能以X结尾
                r'\b\d{14,17}[\dXx]\b',
                # 超宽松模式：连续的长数字串（用于OCR识别错误的情况）
                r'\b\d{15,18}\b',
            ],
            'bank_card': [
                r'\d{4}\s*\d{4}\s*\d{4}\s*\d{4}',
                r'\d{4}[\s\-]*\d{4}[\s\-]*\d{4}[\s\-]*\d{4}',
                r'\d{15}',
                r'\d{19}',
                r'4\d{15}',
                r'5[1-5]\d{14}',
                r'62\d{14,17}',
                r'\d{13,19}',
            ],
            'name': [
                # 中文姓名模式：姓氏 + 1-3个中文字符，总长度不超过4个字
                r'[王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}',
            ],
            'address_numbers': [
                r'\d+号',
                r'\d+栋',
                r'\d+楼',
                r'\d+室',
                r'\d+单元',
                r'\d+弄',
                r'\d+巷',
            ]
        }

        try:
            if self.enable_yolo:
                if not yolo_model_paths:
                    raise ValueError("未提供 YOLO 模型路径")
                for path in yolo_model_paths:
                    if not Path(path).is_file():
                        raise FileNotFoundError(f"YOLO 模型文件不存在: {path}")
                    model = YOLO(path)
                    self.yolo_models.append(model)
                    self.yolo_classes[path] = model.names  # 记录每个模型的类别
                    logger.info(f"YOLOv8 模型加载成功，路径: {path}, 支持类别: {model.names}")
            else:
                logger.warning("YOLO 检测已禁用，将不进行目标检测")

            if self.enable_ocr:
                if not PADDLEOCR_AVAILABLE:
                    raise ImportError("PaddleOCR未安装，请运行: pip install paddleocr==2.7.3")
                
                logger.info("正在初始化 PaddleOCR...")
                self._init_paddleocr(ocr_model_path)
                logger.info(f"PaddleOCR 引擎初始化成功，检测模式: {ocr_mode}, 帧间隔: {ocr_frame_interval}")
            else:
                logger.warning("OCR 检测已禁用，将不进行文字检测")

            logger.info(f"初始化完成 - YOLO 检测: {enable_yolo}, OCR 检测: {enable_ocr}, 加载模型数量: {len(self.yolo_models)}")
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}", exc_info=True)
            raise

    def _get_format_codecs(self, format_type: str):
        """根据格式类型获取对应的编解码器列表"""
        format_codecs = {
            'mp4': [
                ('mp4v', '.mp4'),    # MPEG-4 Part 2 - 最广泛支持的MP4编解码器
                ('H264', '.mp4'),    # H.264编解码器（如果可用）
                ('FMP4', '.mp4'),    # Fragmented MP4
                ('avc1', '.mp4'),    # AVC1编解码器
            ],
            'avi': [
                ('MJPG', '.avi'),    # Motion JPEG - 兼容性极好
                ('XVID', '.avi'),    # XVID - 开源编解码器
                ('mp4v', '.avi'),    # MP4V在AVI容器中
                ('DIVX', '.avi'),    # DivX编解码器
            ],
            'mov': [
                ('mp4v', '.mov'),    # MPEG-4 Part 2 for MOV
                ('H264', '.mov'),    # H.264 for MOV
                ('MJPG', '.mov'),    # Motion JPEG for MOV
            ],
            'mkv': [
                ('H264', '.mkv'),    # H.264 for MKV
                ('mp4v', '.mkv'),    # MPEG-4 Part 2 for MKV
                ('XVID', '.mkv'),    # XVID for MKV
            ]
        }
        return format_codecs.get(format_type, format_codecs['mp4'])

    def _init_paddleocr(self, ocr_model_path: str = None):
        """初始化PaddleOCR引擎"""
        try:
            # 确定OCR模型路径
            if ocr_model_path:
                model_storage_dir = ocr_model_path
            else:
                model_storage_dir = str(Path("models/paddleocr").absolute())

            logger.info("🔍 检查本地模型目录...")

            # 检查本地模型目录是否存在
            model_path = Path(model_storage_dir)
            logger.info(f"检查路径: {model_path}")
            logger.info(f"路径存在: {model_path.exists()}")

            if model_path.exists():
                logger.info("✅ 发现本地模型目录")
                det_dir = model_path / 'det'
                rec_dir = model_path / 'rec'
                cls_dir = model_path / 'cls'

                logger.info(f"检查子目录: det={det_dir.exists()}, rec={rec_dir.exists()}, cls={cls_dir.exists()}")

                if det_dir.exists() and rec_dir.exists() and cls_dir.exists():
                    # 使用本地模型参数
                    local_params = {
                        'use_textline_orientation': True,
                        'lang': 'ch',
                        'use_gpu': False,
                        'text_detection_model_dir': str(det_dir),
                        'text_recognition_model_dir': str(rec_dir),
                        'textline_orientation_model_dir': str(cls_dir)
                    }

                    logger.info(f"使用本地模型参数: {local_params}")
                    self.ocr_engine = PaddleOCR(**local_params)
                    logger.info("✅ PaddleOCR 引擎初始化成功（本地模型）")
                else:
                    logger.warning("⚠️  本地模型目录不完整，使用默认模型")
                    self.ocr_engine = PaddleOCR(use_textline_orientation=True, lang='ch', use_gpu=False)
                    logger.info("✅ PaddleOCR 引擎初始化成功（默认模型）")
            else:
                logger.warning(f"⚠️  本地模型目录不存在: {model_storage_dir}")
                logger.info("将使用 PaddleOCR 默认模型")
                self.ocr_engine = PaddleOCR(use_textline_orientation=True, lang='ch', use_gpu=False)
                logger.info("✅ PaddleOCR 引擎初始化成功（默认模型）")

        except Exception as e:
            logger.error(f"PaddleOCR初始化失败: {e}")
            raise

    @property
    def progress(self) -> Dict[str, float]:
        if not self._processing or self._total_frames == 0:
            return {"progress": 0.0, "elapsed": 0.0, "remaining": 0.0}
        elapsed = time.time() - self._start_time
        progress = min(1.0, self._current_frame / self._total_frames)
        remaining = elapsed / progress * (1 - progress) if progress > 0 else 0.0
        return {
            "progress": round(progress, 4),
            "elapsed": round(elapsed, 2),
            "remaining": round(remaining, 2)
        }

    def apply_mosaic(self, roi: np.ndarray, intensity: int = 5) -> np.ndarray:
        """增强版马赛克效果，确保完整掩盖"""
        try:
            if roi.size == 0:
                logger.warning("尝试处理空ROI")
                return roi
            h, w = roi.shape[:2]
            if h < intensity or w < intensity:
                return roi
            # 缩小到极低分辨率，确保细节丢失
            temp = cv2.resize(roi, (intensity, intensity), interpolation=cv2.INTER_LINEAR)
            mosaic = cv2.resize(temp, (w, h), interpolation=cv2.INTER_NEAREST)
            # 多重模糊处理，确保完全掩盖
            mosaic = cv2.GaussianBlur(mosaic, (21, 21), 0)
            mosaic = cv2.medianBlur(mosaic, 15)
            return mosaic
        except Exception as e:
            logger.error(f"马赛克处理失败: {str(e)}", exc_info=True)
            return roi

    def apply_text_desensitization(self, roi: np.ndarray, method: str = 'mosaic') -> np.ndarray:
        """对文字区域应用脱敏处理"""
        try:
            if roi.size == 0:
                return roi

            if method == 'mosaic':
                return self.apply_mosaic(roi, intensity=3)  # 文字用更细的马赛克
            elif method == 'black_bar':
                return np.zeros_like(roi)  # 黑条遮挡
            elif method == 'blur':
                return cv2.GaussianBlur(roi, (15, 15), 0)  # 高斯模糊
            else:
                return self.apply_mosaic(roi, intensity=3)
        except Exception as e:
            logger.error(f"文字脱敏处理失败: {str(e)}", exc_info=True)
            return roi

    def clean_ocr_text(self, text: str) -> str:
        """清理OCR识别错误"""
        cleaned = text
        for sep in ['-', '_', ':', ',', '.', '|', '/', '\\']:
            cleaned = cleaned.replace(sep, '')

        if len(cleaned) >= 10 and any(c.isdigit() for c in cleaned):
            corrections = {'O': '0', 'o': '0', 'l': '1', 'I': '1', 'S': '5', 'Z': '2', 'G': '6', 'B': '8'}
            for wrong, correct in corrections.items():
                cleaned = cleaned.replace(wrong, correct)

        return cleaned

    def validate_id_card_number(self, text: str) -> bool:
        """验证身份证号码 - 简化版本，移除过度校验"""
        if not text:
            return False

        # 清理文本：只保留数字和X
        clean_text = ''.join(c for c in text.upper() if c.isdigit() or c == 'X')

        # 只检查长度：15位或18位
        if len(clean_text) not in [15, 18]:
            return False

        # 只检查基本格式，不做复杂验证
        if len(clean_text) == 18:
            # 18位身份证：前17位必须是数字，最后一位可以是数字或X
            return clean_text[:17].isdigit() and (clean_text[17].isdigit() or clean_text[17] == 'X')
        elif len(clean_text) == 15:
            # 15位身份证：全部必须是数字
            return clean_text.isdigit()

        return False

    def validate_bank_card_number(self, text: str) -> bool:
        """验证银行卡号的基本格式"""
        if not text:
            return False

        clean_text = ''.join(c for c in text if c.isdigit())

        if len(clean_text) < 13 or len(clean_text) > 19:
            return False

        if len(set(clean_text)) == 1:
            return False

        valid_bins = ['4', '51', '52', '53', '54', '55', '62', '34', '37', '35', '95', '96', '97', '98', '99', '30', '36', '38']

        has_valid_bin = any(clean_text.startswith(bin_code) for bin_code in valid_bins)

        def luhn_check(card_num):
            try:
                def digits_of(n):
                    return [int(d) for d in str(n)]
                digits = digits_of(card_num)
                odd_digits = digits[-1::-2]
                even_digits = digits[-2::-2]
                checksum = sum(odd_digits)
                for d in even_digits:
                    checksum += sum(digits_of(d*2))
                return checksum % 10 == 0
            except:
                return False

        luhn_valid = luhn_check(clean_text)
        is_valid = has_valid_bin or luhn_valid

        return is_valid

    def validate_name(self, text: str) -> bool:
        """验证姓名格式：长度不超过4个字符，且符合中文姓名规律"""
        if not text:
            return False

        # 去除空格和标点符号
        clean_text = ''.join(c for c in text if '\u4e00' <= c <= '\u9fff')

        # 检查长度：1-4个中文字符
        if len(clean_text) < 1 or len(clean_text) > 4:
            return False

        # 检查是否全是中文字符
        if not all('\u4e00' <= c <= '\u9fff' for c in clean_text):
            return False

        # 排除明显的非姓名词汇
        non_name_keywords = [
            # 证件相关
            "准驾车型", "车型", "驾驶", "证件", "身份证", "驾照", "护照",
            # 机构相关
            "公安局", "安局公安", "派出所", "政府", "机关", "部门", "单位",
            "公安", "局公", "安局", "出所", "所派",
            # 地址相关
            "街道", "社区", "村委", "居委", "小区", "大厦", "广场", "中心",
            # 其他常见非姓名词汇
            "有效期", "签发", "机关", "日期", "年月", "到期", "长期",
            "男性", "女性", "性别", "民族", "汉族", "出生", "住址"
        ]

        # 检查是否包含非姓名关键词
        for keyword in non_name_keywords:
            if keyword in clean_text:
                return False

        # 正则表达式已经检查了姓氏，这里直接返回True
        return True

    def is_sensitive_text(self, text: str) -> Tuple[bool, str]:
        """智能检查文本是否包含敏感信息"""
        original_text = text
        text = self.clean_ocr_text(text)

        # 打印文字清理结果
        if original_text != text:
            print(f"🧹 文字清理: '{original_text}' -> '{text}'")

        non_sensitive_texts = [
            "中华人民共和国", "居民身份证", "签发机关", "公安局","安局公安"
            "PEOPLE'S REPUBLIC OF CHINA", "IDENTITY CARD",
            "男", "女", "性别", "民族", "汉", "出生", "住址", "公民身份号码",
            "有效期限", "VALID THRU", "签发日期", "DATE OF ISSUE",
            "中国", "CHINA", "公民", "CITIZEN", "民族", "NATION",
            "出生日期", "DATE OF BIRTH", "性别", "SEX", "住址", "ADDRESS",
            "公安", "公安厅", "派出所", "分局", "支队", "大队", "政府", "机关",
            "日期", "DATE", "编号", "NO.", "NUMBER", "年", "月", "日",
            "YEAR", "MONTH", "DAY", "签名", "SIGNATURE", "照片", "PHOTO",
            "中华", "人民", "共和", "共和国", "人民共和国", "中华人民", "人民共", "共利", "利囤",
            "有效期限", "签发机关", "公民身份号码", "居民身份证", "身份证",
            "VALID", "THRU", "DATE", "ISSUE", "IDENTITY", "CARD", "CITIZEN",
        ]

        # 特殊处理：如果文字包含"姓名"等标签，提取实际的姓名部分
        name_labels = ["姓名", "NAME", "姓", "名"]
        extracted_name = None
        for label in name_labels:
            if label in original_text:
                # 提取标签后面的内容作为实际姓名
                parts = original_text.split(label)
                if len(parts) > 1:
                    potential_name = parts[1].strip()
                    # 清理可能的分隔符
                    potential_name = potential_name.replace(":", "").replace("：", "").strip()
                    if potential_name and len(potential_name) <= 4:
                        extracted_name = potential_name
                        print(f"🏷️ 从标签文字中提取姓名: '{original_text}' -> '{extracted_name}'")
                        break

        # 如果提取到了姓名，直接检查姓名
        if extracted_name:
            is_name_sensitive, _ = self.is_sensitive_text(extracted_name)
            if is_name_sensitive:
                print(f"✅ 确认从标签中提取的敏感姓名: '{extracted_name}'")
                return True, "name"

        # 完全匹配的非敏感信息检查
        for non_sensitive in non_sensitive_texts:
            if original_text == non_sensitive or text == non_sensitive:
                logger.debug(f"⏭️ 跳过非敏感信息: '{original_text}'")
                return False, ""

        china_parts = ["中华", "人民", "共和", "共利", "利囤", "华人", "民共", "和国"]
        if any(part in original_text or part in text for part in china_parts):
            logger.debug(f"⏭️ 跳过中华人民共和国相关文字: '{original_text}'")
            return False, ""

        date_patterns = [
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}\.\d{2}\.\d{2}-\d{4}\.\d{2}\.\d{2}',
            r'\d{4}-\d{2}-\d{2}-\d{4}-\d{2}-\d{2}',
        ]

        for pattern in date_patterns:
            if re.search(pattern, original_text) or re.search(pattern, text):
                logger.debug(f"⏭️ 跳过日期格式: '{original_text}'")
                return False, ""

        if self.ocr_mode == 'all':
            patterns_to_check = ['id_card', 'bank_card', 'name', 'address_numbers']
        elif self.ocr_mode == 'id_card':
            patterns_to_check = ['id_card', 'name']
        elif self.ocr_mode == 'bank_card':
            patterns_to_check = ['bank_card']
        elif self.ocr_mode == 'driver_license':
            patterns_to_check = ['id_card', 'name', 'address_numbers']
        else:
            patterns_to_check = ['id_card', 'bank_card', 'name', 'address_numbers']

        for pattern_type in patterns_to_check:
            if pattern_type in self.sensitive_patterns:
                for pattern in self.sensitive_patterns[pattern_type]:
                    for check_text in [original_text, text]:
                        if re.search(pattern, check_text, re.IGNORECASE):
                            print(f"🎯 模式匹配: '{original_text}' 匹配 {pattern_type} 模式: {pattern}")
                            if pattern_type == 'id_card':
                                validation_result = self.validate_id_card_number(check_text)
                                print(f"🆔 身份证验证: '{check_text}' -> {validation_result}")
                                if validation_result:
                                    print(f"✅ 确认敏感信息 ({pattern_type}): '{original_text}'")
                                    logger.debug(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                    return True, pattern_type
                            elif pattern_type == 'bank_card':
                                validation_result = self.validate_bank_card_number(check_text)
                                print(f"💳 银行卡验证: '{check_text}' -> {validation_result}")
                                if validation_result:
                                    print(f"✅ 确认敏感信息 ({pattern_type}): '{original_text}'")
                                    logger.debug(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                    return True, pattern_type
                            elif pattern_type == 'name':
                                validation_result = self.validate_name(check_text)
                                print(f"👤 姓名验证: '{check_text}' -> {validation_result}")
                                if validation_result:
                                    print(f"✅ 确认敏感信息 ({pattern_type}): '{original_text}'")
                                    logger.debug(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                    return True, pattern_type
                            else:
                                print(f"✅ 确认敏感信息 ({pattern_type}): '{original_text}'")
                                logger.debug(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                return True, pattern_type

        return False, ""

    def detect_text_regions(self, frame: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """使用PaddleOCR检测帧中的敏感文字区域"""
        if not self.enable_ocr or self.ocr_engine is None:
            return []

        results = []
        try:
            ocr_results = self.ocr_engine.ocr(frame, cls=True)

            if not ocr_results or not ocr_results[0]:
                return []

            for i, line in enumerate(ocr_results[0]):
                if len(line) >= 2:
                    coords = line[0]  # 边界框坐标
                    text_info = line[1]  # (文本, 置信度)

                    if isinstance(text_info, tuple) and len(text_info) >= 2:
                        text, confidence = text_info[0], text_info[1]

                        # 打印所有识别的文字（用于调试）
                        print(f"🔍 帧 {self._current_frame}: 识别文字: '{text}' (置信度: {confidence:.3f})")
                        logger.debug(f"帧 {self._current_frame}: 识别文字: '{text}' (置信度: {confidence:.3f})")

                        # 检查是否为敏感信息
                        is_sensitive, info_type = self.is_sensitive_text(text)

                        # 打印敏感信息检测结果
                        if is_sensitive:
                            print(f"⚠️  帧 {self._current_frame}: 检测到敏感信息: '{text}' -> 类型: {info_type} (置信度: {confidence:.3f})")
                        else:
                            print(f"✅ 帧 {self._current_frame}: 非敏感信息: '{text}' (置信度: {confidence:.3f})")

                        if is_sensitive and confidence > 0.5:  # 置信度阈值
                            # 计算边界框
                            x_coords = [point[0] for point in coords]
                            y_coords = [point[1] for point in coords]
                            x = int(min(x_coords))
                            y = int(min(y_coords))
                            w = int(max(x_coords) - min(x_coords))
                            h = int(max(y_coords) - min(y_coords))

                            # 扩展边界框以确保完全覆盖
                            padding = 5
                            x = max(0, x - padding)
                            y = max(0, y - padding)
                            w += 2 * padding
                            h += 2 * padding

                            results.append((x, y, w, h, f"敏感文字_{info_type}"))
                            logger.info(f"帧 {self._current_frame}: 检测到敏感文字: {text} ({info_type}) - 位置: ({x}, {y}), 大小: ({w}, {h}), 置信度: {confidence:.2f}")

            logger.debug(f"帧 {self._current_frame}: PaddleOCR检测完成，发现 {len(results)} 个敏感文字区域")
        except Exception as e:
            logger.error(f"PaddleOCR文字检测失败: {str(e)}", exc_info=True)

        return results

    def detect_objects(self, frame: np.ndarray) -> list:
        """检测帧中的目标和敏感文字"""
        if frame is None or frame.size == 0:
            logger.warning("接收到空帧")
            return []
        results = []

        try:
            # YOLO目标检测
            if self.enable_yolo:
                for model_idx, model in enumerate(self.yolo_models):
                    model_path = list(self.yolo_classes.keys())[model_idx]  # 获取模型路径
                    yolo_results = model(frame, imgsz=320, conf=0.3)  # 动态检测所有目标
                    for result in yolo_results:
                        for box in result.boxes:
                            x, y, x2, y2 = map(int, box.xyxy[0])
                            w, h = x2 - x, y2 - y
                            if w > 0 and h > 0:
                                cls_id = int(box.cls[0])
                                label = self.yolo_classes[model_path][cls_id]
                                conf = float(box.conf[0])
                                results.append((x, y, w, h, label))
                                logger.debug(
                                    f"模型 {model_idx} ({model_path}) - 检测到目标 - 类别: {label}, 置信度: {conf:.2f}, 位置: ({x}, {y}), 大小: ({w}, {h})")
                        logger.debug(f"帧 {self._current_frame}: 模型 {model_idx} 检测到 {len(result.boxes)} 个目标")

            # PaddleOCR敏感文字检测（针对动态内容优化）
            if self.enable_ocr:
                # 智能检测策略：根据内容动态调整检测频率
                should_run_ocr = False

                if self.ocr_frame_interval <= 5:
                    # 高频检测模式：适合动态内容多的视频
                    should_run_ocr = (
                        self._current_frame < 20 or  # 前20帧总是检测
                        self._current_frame % self.ocr_frame_interval == 0  # 按设定间隔检测
                    )
                elif self.ocr_frame_interval <= 15:
                    # 中频检测模式：平衡性能和准确性
                    should_run_ocr = (
                        self._current_frame < 15 or  # 前15帧总是检测
                        self._current_frame % max(3, self.ocr_frame_interval // 2) == 0  # 减半间隔
                    )
                else:
                    # 低频检测模式：性能优先
                    should_run_ocr = (
                        self._current_frame < 10 or  # 前10帧总是检测
                        self._current_frame % max(5, self.ocr_frame_interval // 3) == 0  # 减少间隔
                    )

                if should_run_ocr:
                    text_regions = self.detect_text_regions(frame)
                    results.extend(text_regions)
                    self._last_ocr_results = text_regions  # 缓存OCR结果
                    print(f"🔍 帧 {self._current_frame}: 执行OCR检测，发现 {len(text_regions)} 个敏感区域")
                    logger.debug(f"帧 {self._current_frame}: 执行PaddleOCR检测，发现 {len(text_regions)} 个敏感文字区域")
                elif self._last_ocr_results:
                    # 使用缓存的OCR结果
                    results.extend(self._last_ocr_results)
                    logger.debug(f"帧 {self._current_frame}: 使用缓存的OCR结果，共 {len(self._last_ocr_results)} 个敏感文字区域")

        except Exception as e:
            logger.error(f"目标检测失败: {str(e)}", exc_info=True)

        return results

    def process_video(
            self,
            input_path: str,
            output_path: str,
            skip_frames: int = 0,
            mosaic_intensity: int = 5,
            text_desensitization_method: str = 'mosaic',
            progress_callback: Optional[Callable] = None,
            completion_callback: Optional[Callable] = None
    ) -> None:
        logger.info(f"开始处理视频: {input_path} -> {output_path}")
        self._processing = True
        self._start_time = time.time()

        cap = None
        out = None
        try:
            input_path = str(Path(input_path).resolve())
            output_path = str(Path(output_path).resolve())

            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"输出目录: {output_dir}")

            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise IOError(f"无法打开视频文件: {input_path}")

            self._total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            logger.info(f"视频信息 - 帧数: {self._total_frames}, FPS: {fps}, 分辨率: {width}x{height}")

            # 根据输出格式参数选择编解码器
            out = None
            final_output_path = None
            successful_codec = None

            # 确定目标输出格式和输出路径
            target_format = self.output_format
            input_path_obj = Path(input_path)
            output_path_obj = Path(output_path)

            if target_format == 'same':
                # 与输入格式一致，保持相同的文件名和扩展名
                input_ext = input_path_obj.suffix.lower()
                input_filename = input_path_obj.name  # 包含扩展名的完整文件名

                # 如果输出路径是目录，则在该目录下使用相同的文件名
                if output_path_obj.is_dir() or not output_path_obj.suffix:
                    output_path = str(output_path_obj / input_filename)
                    logger.info(f"🔄 输出路径为目录，使用相同文件名: {output_path}")

                # 确定目标格式
                if input_ext == '.mp4':
                    target_format = 'mp4'
                elif input_ext == '.avi':
                    target_format = 'avi'
                elif input_ext == '.mov':
                    target_format = 'mov'
                elif input_ext == '.mkv':
                    target_format = 'mkv'
                elif input_ext == '.wmv':
                    target_format = 'avi'  # WMV使用AVI容器
                elif input_ext == '.flv':
                    target_format = 'avi'  # FLV使用AVI容器
                elif input_ext == '.webm':
                    target_format = 'mkv'  # WebM使用MKV容器
                elif input_ext == '.3gp':
                    target_format = 'mp4'  # 3GP使用MP4容器
                elif input_ext == '.m4v':
                    target_format = 'mp4'  # M4V使用MP4容器
                else:
                    target_format = 'mp4'  # 默认回退到MP4

                logger.info(f"🔄 输入文件: {input_filename}, 输出文件: {Path(output_path).name}, 目标格式: {target_format}")
            else:
                logger.info(f"🔄 指定输出格式: {target_format}")

            if target_format == 'auto':
                # 自动模式：优先MP4，失败后回退
                primary_formats = self._get_format_codecs('mp4')
                fallback_formats = [
                    ('MJPG', '.avi'),    # Motion JPEG - Docker环境最兼容
                    ('XVID', '.avi'),    # XVID - 开源编解码器
                    ('mp4v', '.avi'),    # MP4V在AVI容器中
                    ('DIVX', '.avi'),    # DivX编解码器
                ]
                logger.info("🎯 自动模式：优先尝试MP4格式...")
            else:
                # 指定格式模式
                primary_formats = self._get_format_codecs(target_format)
                fallback_formats = []  # 指定格式时不使用回退
                logger.info(f"🎯 指定格式模式：{target_format.upper()}")

            # 第一阶段：尝试主要格式
            for codec, ext in primary_formats:
                try:
                    primary_output_path = str(Path(output_path).with_suffix(ext))
                    logger.info(f"🔄 尝试编解码器: {codec} -> {ext}")
                    fourcc = cv2.VideoWriter_fourcc(*codec)
                    out = cv2.VideoWriter(primary_output_path, fourcc, fps, (width, height))

                    # 验证VideoWriter是否成功创建
                    if out and out.isOpened():
                        # 创建测试帧并尝试写入
                        test_frame = np.zeros((height, width, 3), dtype=np.uint8)
                        test_result = out.write(test_frame)

                        # 检查写入是否成功（某些编解码器write()返回None而不是False）
                        if test_result is not False:
                            logger.info(f"✅ 编解码器成功: {codec} ({ext}) -> {primary_output_path}")
                            # 重新创建VideoWriter以确保从头开始
                            out.release()
                            out = cv2.VideoWriter(primary_output_path, fourcc, fps, (width, height))
                            if out and out.isOpened():
                                final_output_path = primary_output_path
                                successful_codec = f"{codec} ({ext})"
                                break
                            else:
                                logger.warning(f"⚠️  编解码器 {codec} 重新创建失败")
                                out = None
                        else:
                            logger.warning(f"⚠️  编解码器 {codec} 写入测试失败")
                            out.release()
                            out = None
                    else:
                        logger.warning(f"⚠️  编解码器 {codec} 无法创建VideoWriter")
                        if out:
                            out.release()
                        out = None

                except Exception as e:
                    logger.warning(f"❌ 编解码器 {codec} 发生异常: {str(e)}")
                    if out:
                        try:
                            out.release()
                        except:
                            pass
                    out = None

            # 第二阶段：如果主要格式都失败，尝试回退格式（仅auto模式）
            if not out or not out.isOpened():
                if fallback_formats:
                    logger.warning("⚠️  主要格式都失败，尝试回退格式...")
                else:
                    logger.error(f"❌ 指定格式 {target_format} 的所有编解码器都失败")

                for codec, ext in fallback_formats:
                    try:
                        fallback_output_path = str(Path(output_path).with_suffix(ext))
                        logger.info(f"🔄 尝试回退格式: {codec} -> {ext}")
                        fourcc = cv2.VideoWriter_fourcc(*codec)
                        out = cv2.VideoWriter(fallback_output_path, fourcc, fps, (width, height))

                        # 验证VideoWriter是否成功创建
                        if out and out.isOpened():
                            # 创建测试帧并尝试写入
                            test_frame = np.zeros((height, width, 3), dtype=np.uint8)
                            test_result = out.write(test_frame)

                            # 检查写入是否成功
                            if test_result is not False:
                                logger.info(f"✅ 回退格式成功: {codec} ({ext}) -> {fallback_output_path}")
                                # 重新创建VideoWriter以确保从头开始
                                out.release()
                                out = cv2.VideoWriter(fallback_output_path, fourcc, fps, (width, height))
                                if out and out.isOpened():
                                    final_output_path = fallback_output_path
                                    successful_codec = f"{codec} ({ext})"
                                    break
                                else:
                                    logger.warning(f"⚠️  回退格式 {codec} 重新创建失败")
                                    out = None
                            else:
                                logger.warning(f"⚠️  回退格式 {codec} 写入测试失败")
                                out.release()
                                out = None
                        else:
                            logger.warning(f"⚠️  回退格式 {codec} 无法创建VideoWriter")
                            if out:
                                out.release()
                            out = None

                    except Exception as e:
                        logger.warning(f"❌ 回退格式 {codec} 发生异常: {str(e)}")
                        if out:
                            try:
                                out.release()
                            except:
                                pass
                        out = None

            if not out or not out.isOpened():
                raise IOError(f"无法创建输出文件: {output_path}，所有编解码器都失败")

            logger.info(f"🎬 视频编码器选择完成: {successful_codec} -> {final_output_path}")

            last_objects = []
            while self._processing:
                ret, frame = cap.read()
                if not ret:
                    logger.info(f"视频读取完成，共处理 {self._current_frame} 帧")
                    break

                objects = self.detect_objects(frame)
                if objects:
                    last_objects = objects
                elif last_objects and self._current_frame % (skip_frames + 1) != 0:
                    objects = last_objects
                    logger.debug(f"帧 {self._current_frame}: 使用上一帧检测结果，共 {len(last_objects)} 个目标")

                for (x, y, w, h, label) in objects:
                    if x + w <= frame.shape[1] and y + h <= frame.shape[0]:
                        roi = frame[y:y + h, x:x + w]

                        # 根据检测类型选择不同的处理方法
                        if label.startswith("敏感文字_"):
                            processed_roi = self.apply_text_desensitization(roi, text_desensitization_method)
                            logger.debug(f"帧 {self._current_frame}: 对 {label} 应用文字脱敏({text_desensitization_method}) - 位置: ({x}, {y}), 大小: ({w}, {h})")
                        else:
                            processed_roi = self.apply_mosaic(roi, mosaic_intensity)
                            logger.debug(f"帧 {self._current_frame}: 对 {label} 应用马赛克 - 位置: ({x}, {y}), 大小: ({w}, {h})")

                        frame[y:y + h, x:x + w] = processed_roi
                    else:
                        logger.warning(f"检测目标超出边界: {label} at ({x},{y},{w},{h})")

                out.write(frame)

                if progress_callback and self._current_frame % 10 == 0:
                    progress = self.progress
                    progress_callback(progress)
                    logger.info(
                        f"帧 {self._current_frame}: 进度 {progress['progress'] * 100:.1f}%, 已用 {progress['elapsed']}s, 剩余 {progress['remaining']}s")

                self._current_frame += 1

            status = {"status": "success", "output_path": output_path,
                      "total_frames": self._current_frame, "time_used": round(time.time() - self._start_time, 2)}
            logger.info(f"处理完成: {status}")

        except Exception as e:
            status = {"status": "error", "message": str(e)}
            logger.error(f"视频处理异常: {str(e)}", exc_info=True)
        finally:
            if cap is not None:
                cap.release()
            if out is not None:
                out.release()
            self._processing = False
            if completion_callback:
                completion_callback(status)
            logger.info("资源清理完成")

    def stop_processing(self) -> None:
        self._processing = False
        logger.info("收到中止信号")

    def process_videos_batch(
            self,
            input_dir: str,
            output_dir: str,
            skip_frames: int = 0,
            mosaic_intensity: int = 5,
            text_desensitization_method: str = 'mosaic',
            progress_callback: Optional[Callable] = None,
            completion_callback: Optional[Callable] = None
    ) -> None:
        """
        批量处理目录中的所有视频文件
        :param input_dir: 输入目录路径
        :param output_dir: 输出目录路径
        :param skip_frames: 跳帧数
        :param mosaic_intensity: 马赛克强度
        :param text_desensitization_method: 文字脱敏方法
        :param progress_callback: 进度回调函数
        :param completion_callback: 完成回调函数
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)

        if not input_path.exists() or not input_path.is_dir():
            raise ValueError(f"输入目录不存在或不是目录: {input_dir}")

        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"输出目录: {output_path}")

        # 支持的视频文件扩展名（只使用小写，glob会自动匹配大小写）
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.3gp', '.m4v'}

        # 查找所有视频文件（使用集合去重，避免重复文件）
        video_files = set()
        for ext in video_extensions:
            # 同时匹配小写和大写扩展名
            video_files.update(input_path.glob(f'*{ext}'))
            video_files.update(input_path.glob(f'*{ext.upper()}'))

        # 转换为列表并按文件名排序
        video_files = sorted(list(video_files))

        if not video_files:
            logger.warning(f"输入目录 {input_dir} 中未找到视频文件")
            return

        logger.info(f"发现 {len(video_files)} 个视频文件待处理")

        successful_count = 0
        failed_count = 0

        for i, video_file in enumerate(video_files, 1):
            try:
                # 生成输出文件名（根据输出格式设置扩展名）
                if self.output_format == 'same':
                    # 保持与输入相同的扩展名
                    output_file = output_path / video_file.name
                elif self.output_format == 'mp4':
                    # 强制MP4格式
                    output_file = output_path / f"{video_file.stem}.mp4"
                else:
                    # 其他格式
                    format_ext_map = {
                        'avi': '.avi',
                        'mov': '.mov',
                        'mkv': '.mkv',
                        'auto': '.mp4'  # auto模式默认MP4
                    }
                    ext = format_ext_map.get(self.output_format, '.mp4')
                    output_file = output_path / f"{video_file.stem}{ext}"

                logger.info(f"[{i}/{len(video_files)}] 开始处理: {video_file.name}")

                # 为批量处理创建单独的进度回调
                def batch_progress_callback(progress: dict):
                    if progress_callback:
                        # 添加批量处理的额外信息
                        batch_progress = {
                            **progress,
                            'current_file': i,
                            'total_files': len(video_files),
                            'current_filename': video_file.name,
                            'overall_progress': (i - 1 + progress['progress']) / len(video_files)
                        }
                        progress_callback(batch_progress)

                # 为批量处理创建单独的完成回调
                def batch_completion_callback(result: dict):
                    nonlocal successful_count, failed_count
                    if result["status"] == "success":
                        successful_count += 1
                        logger.info(f"✅ [{i}/{len(video_files)}] 处理成功: {video_file.name} -> {output_file.name}")
                    else:
                        failed_count += 1
                        logger.error(f"❌ [{i}/{len(video_files)}] 处理失败: {video_file.name} - {result.get('message', '未知错误')}")

                # 处理单个视频
                self.process_video(
                    input_path=str(video_file),
                    output_path=str(output_file),
                    skip_frames=skip_frames,
                    mosaic_intensity=mosaic_intensity,
                    text_desensitization_method=text_desensitization_method,
                    progress_callback=batch_progress_callback,
                    completion_callback=batch_completion_callback
                )

            except Exception as e:
                failed_count += 1
                logger.error(f"❌ [{i}/{len(video_files)}] 处理异常: {video_file.name} - {str(e)}", exc_info=True)

        # 批量处理完成总结
        total_processed = successful_count + failed_count
        batch_result = {
            "status": "completed",
            "total_files": len(video_files),
            "successful": successful_count,
            "failed": failed_count,
            "success_rate": f"{(successful_count / total_processed * 100):.1f}%" if total_processed > 0 else "0%",
            "output_dir": str(output_path)
        }

        logger.info(f"🎉 批量处理完成！")
        logger.info(f"📊 处理统计: 总计 {len(video_files)} 个文件, 成功 {successful_count} 个, 失败 {failed_count} 个")
        logger.info(f"📁 输出目录: {output_path}")

        if completion_callback:
            completion_callback(batch_result)


if __name__ == "__main__":
    def print_progress(progress: dict):
        print(
            f"\r处理进度: {progress['progress'] * 100:.1f}% | 已用: {progress['elapsed']}s | 剩余: {progress['remaining']}s",
            end="")


    def on_complete(result: dict):
        print("\n" + "-" * 50)
        if result["status"] == "success":
            print(f"处理完成！输出: {result['output_path']}")
            print(f"总帧数: {result['total_frames']} 耗时: {result['time_used']}s")
        else:
            print(f"处理失败: {result['message']}")


    parser = argparse.ArgumentParser(description='增强版视频马赛克工具 - PaddleOCR版本')
    parser.add_argument('-i', '--input', required=True, help="输入视频路径或目录")
    parser.add_argument('-o', '--output', required=True, help="输出目录路径（默认保持与输入相同的文件名和格式）或完整输出文件路径")
    parser.add_argument('--yolo_models', nargs='+', default=['models/yolov8n-face.pt'], help="YOLOv8 模型路径列表")
    parser.add_argument('-target', '--target', nargs='+', help='YOLO模型目标名称列表（不包含.pt扩展名）')
    parser.add_argument('--skip_frames', type=int, default=0, help="跳帧数")
    parser.add_argument('--intensity', type=int, default=5, help="马赛克强度（越小越模糊）")
    parser.add_argument('--no_yolo', action='store_false', dest='enable_yolo', help="禁用 YOLO 检测")
    parser.add_argument('--enable_ocr', action='store_true', help="启用 PaddleOCR 敏感文字检测")
    parser.add_argument('--ocr_mode', type=str, default='all',
                        choices=['id_card', 'bank_card', 'driver_license', 'all'],
                        help="OCR 检测模式：id_card(身份证), bank_card(银行卡), driver_license(驾驶证), all(全部)")
    parser.add_argument('--text_method', type=str, default='mosaic',
                        choices=['mosaic', 'black_bar', 'blur'],
                        help="文字脱敏方法：mosaic(马赛克), black_bar(黑条), blur(模糊)")
    parser.add_argument('--ocr_interval', type=int, default=15,
                        help="OCR检测帧间隔（每N帧检测一次，数值越小检测越频繁，建议：动态内容多用1-5，一般内容用10-15）")
    parser.add_argument('--dynamic_content', action='store_true',
                        help="动态内容模式：自动设置最高检测频率，等同于 --ocr_interval 1")
    parser.add_argument('--ocr_model_path', type=str, help="PaddleOCR模型路径")
    parser.add_argument('--output_format', type=str, default='same',
                        choices=['same', 'auto', 'mp4', 'avi', 'mov', 'mkv'],
                        help="输出视频格式：same(默认-与输入格式一致), auto(自动选择最佳), mp4(MP4格式), avi(AVI格式), mov(MOV格式), mkv(MKV格式)")
    parser.add_argument('--batch', action='store_true', help="启用批量处理模式（输入输出为目录）")
    args = parser.parse_args()

    # 处理YOLO模型路径 - 与图片脚本保持一致
    if args.target and args.enable_yolo:
        # 根据target参数构建模型路径
        args.yolo_models = []
        for target in args.target:
            model_path = f"models/{target}.pt"
            args.yolo_models.append(model_path)
        logger.info(f"根据target参数构建模型路径: {args.yolo_models}")
    elif args.yolo_models:
        logger.info(f"使用指定的模型路径: {args.yolo_models}")
    elif args.enable_yolo and not args.yolo_models:
        # 使用默认模型
        logger.info(f"使用默认YOLO模型: {args.yolo_models}")

    # 验证参数
    if not args.enable_yolo and not args.enable_ocr:
        logger.error("必须启用 YOLO 检测或 OCR 检测中的至少一个功能")
        sys.exit(1)

    if args.enable_yolo and not args.yolo_models:
        logger.error("启用 YOLO 检测时必须提供模型路径")
        sys.exit(1)

    if args.enable_ocr and not PADDLEOCR_AVAILABLE:
        logger.error("PaddleOCR未安装，请运行: pip install paddleocr==2.7.3")
        sys.exit(1)

    # 处理动态内容模式
    if args.dynamic_content:
        args.ocr_interval = 1  # 设置为最高检测频率
        print("🚀 启用动态内容模式：OCR检测间隔设置为1帧（最高检测率）")
        logger.info("启用动态内容模式：OCR检测间隔设置为1帧")

    try:
        processor = VideoProcessorPaddle(
            yolo_model_paths=args.yolo_models or [],
            enable_yolo=args.enable_yolo,
            enable_ocr=args.enable_ocr,
            ocr_mode=args.ocr_mode,
            ocr_frame_interval=args.ocr_interval,
            ocr_model_path=args.ocr_model_path,
            output_format=args.output_format
        )

        if args.batch:
            # 批量处理模式
            def batch_print_progress(progress: dict):
                if 'current_file' in progress:
                    print(f"\r[{progress['current_file']}/{progress['total_files']}] {progress['current_filename']} | "
                          f"文件进度: {progress['progress'] * 100:.1f}% | 总进度: {progress['overall_progress'] * 100:.1f}% | "
                          f"已用: {progress['elapsed']}s | 剩余: {progress['remaining']}s", end="")
                else:
                    print_progress(progress)

            def batch_on_complete(result: dict):
                print("\n" + "=" * 60)
                if result["status"] == "completed":
                    print(f"🎉 批量处理完成！")
                    print(f"📊 处理统计:")
                    print(f"   总文件数: {result['total_files']}")
                    print(f"   成功处理: {result['successful']}")
                    print(f"   处理失败: {result['failed']}")
                    print(f"   成功率: {result['success_rate']}")
                    print(f"📁 输出目录: {result['output_dir']}")
                else:
                    print(f"❌ 批量处理失败: {result.get('message', '未知错误')}")

            processor.process_videos_batch(
                input_dir=args.input,
                output_dir=args.output,
                skip_frames=max(0, args.skip_frames),
                mosaic_intensity=args.intensity,
                text_desensitization_method=args.text_method,
                progress_callback=batch_print_progress,
                completion_callback=batch_on_complete
            )
        else:
            # 单文件处理模式
            processor.process_video(
                input_path=args.input,
                output_path=args.output,
                skip_frames=max(0, args.skip_frames),
                mosaic_intensity=args.intensity,
                text_desensitization_method=args.text_method,
                progress_callback=print_progress,
                completion_callback=on_complete
            )
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}", exc_info=True)
        sys.exit(1)
