#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试姓名检测功能，特别是地名排除功能
"""

from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mosaic_image_paddle import VideoProcessorPaddle

def test_name_detection():
    """测试姓名检测功能"""
    
    # 创建测试处理器
    processor = VideoProcessorPaddle(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=True,
        ocr_mode='name',
        output_format='same'
    )
    
    print("🧪 测试姓名检测功能...")
    
    # 姓名测试用例
    name_test_cases = [
        # 有效姓名
        ("张三", True, "常见姓名"),
        ("李四", True, "常见姓名"),
        ("王小明", True, "三字姓名"),
        ("欧阳锋", True, "复姓"),
        ("赵子龙", True, "三字姓名"),
        ("刘德华", True, "明星姓名"),
        
        # 地名（应该被排除）
        ("江苏徐州", False, "省市组合"),
        ("浙江杭州", False, "省市组合"),
        ("广东深圳", False, "省市组合"),
        ("山东济南", False, "省市组合"),
        ("河南郑州", False, "省市组合"),
        ("湖北武汉", False, "省市组合"),
        ("四川成都", False, "省市组合"),
        ("北京市", False, "直辖市"),
        ("上海市", False, "直辖市"),
        ("重庆市", False, "直辖市"),
        ("天津市", False, "直辖市"),
        
        # 其他非姓名
        ("身份证", False, "证件类型"),
        ("驾驶证", False, "证件类型"),
        ("有效期", False, "时间相关"),
        ("公安局", False, "机构名称"),
        ("派出所", False, "机构名称"),
        
        # 边界情况
        ("江", True, "单字姓氏（可能是真姓名）"),
        ("浙", True, "单字姓氏（可能是真姓名）"),
        ("江小白", True, "以地名字开头但可能是真姓名"),
        ("浙小明", True, "以地名字开头但可能是真姓名"),
    ]
    
    print("\n👤 测试姓名验证:")
    for name, expected, description in name_test_cases:
        result = processor.validate_name(name)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {name:<10} -> {result:<5} ({description})")
    
    print("\n🔍 测试敏感信息检测:")
    # 测试敏感信息检测
    text_test_cases = [
        ("我叫张三", True, "name"),
        ("联系人：李四", True, "name"),
        ("地址：江苏徐州", False, ""),  # 地名不应该被识别为姓名
        ("来自浙江杭州", False, ""),  # 地名不应该被识别为姓名
        ("户籍：广东深圳", False, ""),  # 地名不应该被识别为姓名
        ("这是普通文字", False, ""),
    ]
    
    for text, expected_sensitive, expected_type in text_test_cases:
        is_sensitive, info_type = processor.is_sensitive_text(text)
        status = "✅" if (is_sensitive == expected_sensitive and (not expected_sensitive or info_type == expected_type)) else "❌"
        print(f"  {status} '{text}' -> 敏感: {is_sensitive}, 类型: {info_type}")
    
    print(f"\n🎉 姓名检测测试完成！")

if __name__ == "__main__":
    test_name_detection()
