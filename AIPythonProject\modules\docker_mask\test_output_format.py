#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输出格式功能
"""

from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mosaic_video_paddle import VideoProcessorPaddle

def test_output_path_handling():
    """测试输出路径处理逻辑"""
    
    # 创建测试处理器
    processor = VideoProcessorPaddle(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=False,
        output_format='same'
    )
    
    # 测试用例
    test_cases = [
        {
            'input': 'test_video.mp4',
            'output': 'output_dir',
            'expected': 'output_dir/test_video.mp4',
            'description': '输出目录，应保持相同文件名'
        },
        {
            'input': 'video.avi',
            'output': 'results',
            'expected': 'results/video.avi',
            'description': '输出目录，保持AVI格式'
        },
        {
            'input': 'movie.mov',
            'output': 'processed/result.mov',
            'expected': 'processed/result.mov',
            'description': '指定完整输出路径'
        }
    ]
    
    print("🧪 测试输出路径处理逻辑...")
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {case['description']}")
        print(f"  输入: {case['input']}")
        print(f"  输出参数: {case['output']}")
        print(f"  期望结果: {case['expected']}")
        
        # 模拟路径处理逻辑
        input_path_obj = Path(case['input'])
        output_path_obj = Path(case['output'])
        
        if processor.output_format == 'same':
            input_filename = input_path_obj.name
            
            # 如果输出路径是目录或没有扩展名，则在该目录下使用相同的文件名
            if not output_path_obj.suffix:
                result_path = str(output_path_obj / input_filename)
            else:
                result_path = case['output']
        else:
            result_path = case['output']
        
        print(f"  实际结果: {result_path}")
        
        if result_path == case['expected']:
            print(f"  ✅ 通过")
        else:
            print(f"  ❌ 失败")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    test_output_path_handling()
