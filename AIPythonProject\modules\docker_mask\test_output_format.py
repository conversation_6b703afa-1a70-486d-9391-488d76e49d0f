#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输出格式功能
"""

from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mosaic_video_paddle import VideoProcessorPaddle

def test_output_path_handling():
    """测试输出路径处理逻辑"""

    # 创建测试处理器
    processor = VideoProcessorPaddle(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=False,
        output_format='same'
    )

    print("🧪 测试单文件输出路径处理逻辑...")

    # 单文件处理测试用例
    single_file_cases = [
        {
            'input': 'test_video.mp4',
            'output': 'output',
            'expected': 'output.mp4',
            'description': '单文件处理 - MP4格式，自动添加扩展名'
        },
        {
            'input': 'video.avi',
            'output': 'result',
            'expected': 'result.avi',
            'description': '单文件处理 - AVI格式，自动添加扩展名'
        },
        {
            'input': 'movie.mov',
            'output': 'processed.mov',
            'expected': 'processed.mov',
            'description': '单文件处理 - 已指定扩展名，保持不变'
        },
        {
            'input': 'clip.mkv',
            'output': 'final',
            'expected': 'final.mkv',
            'description': '单文件处理 - MKV格式，自动添加扩展名'
        }
    ]

    for i, case in enumerate(single_file_cases, 1):
        print(f"\n测试 {i}: {case['description']}")
        print(f"  输入: {case['input']}")
        print(f"  输出参数: {case['output']}")
        print(f"  期望结果: {case['expected']}")

        # 模拟单文件处理的路径逻辑
        input_path_obj = Path(case['input'])
        output_path_obj = Path(case['output'])

        if processor.output_format == 'same':
            input_ext = input_path_obj.suffix.lower()

            # 如果输出路径没有扩展名，自动添加与输入相同的扩展名
            if not output_path_obj.suffix:
                result_path = str(output_path_obj.with_suffix(input_ext))
            else:
                result_path = case['output']
        else:
            result_path = case['output']

        print(f"  实际结果: {result_path}")

        if result_path == case['expected']:
            print(f"  ✅ 通过")
        else:
            print(f"  ❌ 失败")

def test_batch_processing():
    """测试批量处理的输出路径逻辑"""

    print(f"\n🧪 测试批量处理输出路径逻辑...")

    # 批量处理测试用例
    batch_cases = [
        {
            'input_file': 'video1.mp4',
            'output_dir': 'output_folder',
            'expected': 'output_folder/video1.mp4',
            'description': '批量处理 - MP4文件保持扩展名'
        },
        {
            'input_file': 'movie.avi',
            'output_dir': 'results',
            'expected': 'results/movie.avi',
            'description': '批量处理 - AVI文件保持扩展名'
        },
        {
            'input_file': 'clip.mov',
            'output_dir': 'processed',
            'expected': 'processed/clip.mov',
            'description': '批量处理 - MOV文件保持扩展名'
        },
        {
            'input_file': 'test.mkv',
            'output_dir': 'final',
            'expected': 'final/test.mkv',
            'description': '批量处理 - MKV文件保持扩展名'
        }
    ]

    for i, case in enumerate(batch_cases, 1):
        print(f"\n批量测试 {i}: {case['description']}")
        print(f"  输入文件: {case['input_file']}")
        print(f"  输出目录: {case['output_dir']}")
        print(f"  期望结果: {case['expected']}")

        # 模拟批量处理的路径逻辑
        video_file = Path(case['input_file'])
        output_path = Path(case['output_dir'])

        # 批量处理时，output_format='same' 使用原文件名（包含扩展名）
        output_file = output_path / video_file.name
        result_path = str(output_file)

        print(f"  实际结果: {result_path}")

        if result_path == case['expected']:
            print(f"  ✅ 通过")
        else:
            print(f"  ❌ 失败")

    print(f"\n🎉 所有测试完成！")

if __name__ == "__main__":
    test_output_path_handling()
    test_batch_processing()
