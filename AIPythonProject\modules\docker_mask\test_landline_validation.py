#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试固定电话验证功能
"""

from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mosaic_image_paddle import ImageProcessorPaddle

def test_landline_validation():
    """测试固定电话验证功能"""
    
    # 创建测试处理器
    processor = ImageProcessorPaddle(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=True,
        ocr_mode='phone',
        output_format='same'
    )
    
    print("🧪 测试固定电话验证功能...")
    
    # 测试用例
    test_cases = [
        # 图片中出现的格式
        ("(0576) 9572 6", True, "台州固话（图片格式）"),
        ("(021) 3761 4890", True, "上海固话（图片格式）"),
        ("(021) 3876 0365", True, "上海固话（图片格式）"),
        
        # 其他常见格式
        ("021-38764890", True, "上海固话（连字符）"),
        ("0571-88888888", True, "杭州固话（连字符）"),
        ("021 3876 0365", True, "上海固话（空格）"),
        ("0576 9572 6", True, "台州固话（空格）"),
        ("************", True, "400客服电话"),
        ("95588", True, "银行服务热线"),
        ("10086", True, "移动客服"),
        
        # 无效格式
        ("12345678", False, "没有区号"),
        ("0123", False, "太短"),
        ("123456789", False, "不是0开头的固话"),
    ]
    
    print("\n☎️ 测试固定电话验证:")
    for phone, expected, description in test_cases:
        result = processor.validate_landline_phone(phone)
        status = "✅" if result == expected else "❌"
        
        # 显示清理后的数字
        clean_digits = ''.join(c for c in phone if c.isdigit())
        print(f"  {status} {phone:<18} -> {result:<5} (数字: {clean_digits}, 长度: {len(clean_digits)}) - {description}")
    
    print("\n🔍 测试敏感信息检测:")
    # 测试敏感信息检测
    text_test_cases = [
        "(0576) 9572 6",
        "(021) 3761 4890", 
        "(021) 3876 0365",
        "13812345678",
        "这是普通文字",
    ]
    
    for text in text_test_cases:
        is_sensitive, info_type = processor.is_sensitive_text(text)
        status = "✅" if is_sensitive else "❌"
        print(f"  {status} '{text}' -> 敏感: {is_sensitive}, 类型: {info_type}")
    
    print(f"\n🎉 固定电话验证测试完成！")

if __name__ == "__main__":
    test_landline_validation()
