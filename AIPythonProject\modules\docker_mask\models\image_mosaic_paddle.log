[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:947 - 使用指定的模型路径: ['models/yolov8_license_plate.pt']
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:986 - ================================================================================
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:987 - 🚀 PaddleOCR 图片敏感信息脱敏工具启动
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:988 - ================================================================================
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:989 - 📁 输入路径: C:\Users\<USER>\Desktop\1.jpg
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:990 - 📁 输出路径: C:\Users\<USER>\Desktop\2.jpg
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:991 - 🔧 处理模式: 单文件处理
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:992 - 🎯 YOLO检测: 启用
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:993 - 📝 OCR检测: 启用
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:995 - 📝 OCR模式: all
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:996 - 📝 文字脱敏方法: mosaic
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:997 - 📝 文字脱敏强度: 5
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:998 - ================================================================================
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:1001 - 🔧 初始化图片处理器...
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:201 - 🔍 步骤2: 检查本地模型目录...
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:205 - 检查路径: D:\develop\work_project\AILLM\AIPythonProject\modules\docker_mask\models\paddleocr
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:206 - 路径存在: True
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:209 - ✅ 发现本地模型目录
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:214 - 检查子目录: det=True, rec=True, cls=True
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:217 - ✅ 检测模型目录存在: D:\develop\work_project\AILLM\AIPythonProject\modules\docker_mask\models\paddleocr\det
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:219 - ✅ 识别模型目录存在: D:\develop\work_project\AILLM\AIPythonProject\modules\docker_mask\models\paddleocr\rec
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:221 - ✅ 分类模型目录存在: D:\develop\work_project\AILLM\AIPythonProject\modules\docker_mask\models\paddleocr\cls
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:226 - ✅ 步骤2完成: 模型目录检查完毕
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:229 - 🔄 开始初始化 PaddleOCR 引擎...
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:238 - � 使用参数: {'use_textline_orientation': True, 'lang': 'ch', 'use_gpu': False}
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:253 - ✅ 本地模型目录验证通过
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:254 - 🔍 步骤3.2: 准备 PaddleOCR 参数...
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:266 - 使用本地模型参数: {'use_textline_orientation': True, 'lang': 'ch', 'use_gpu': False, 'text_detection_model_dir': 'D:\\develop\\work_project\\AILLM\\AIPythonProject\\modules\\docker_mask\\models\\paddleocr\\det', 'text_recognition_model_dir': 'D:\\develop\\work_project\\AILLM\\AIPythonProject\\modules\\docker_mask\\models\\paddleocr\\rec', 'textline_orientation_model_dir': 'D:\\develop\\work_project\\AILLM\\AIPythonProject\\modules\\docker_mask\\models\\paddleocr\\cls'}
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:267 - 🔍 步骤3.3: 开始调用 PaddleOCR() 构造函数...
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:268 - ⚠️  注意: 这一步可能需要较长时间，请耐心等待...
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:273 - ✅ PaddleOCR() 构造函数调用成功!
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:274 - ✅ PaddleOCR 引擎初始化成功，检测模式: all
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:282 - ============================================================
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:283 - ✅ ImageProcessor 初始化完成!
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:284 - 🎯 YOLO 检测: 启用
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:285 - 📝 OCR 检测: 启用
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:286 - 🔧 加载的 YOLO 模型数量: 1
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:288 - 📝 OCR 检测模式: all
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:289 - ============================================================
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:1013 - ✅ 图片处理器初始化完成
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:785 - 开始处理: 1.jpg
[2025-07-31 09:14:26] [    INFO] mosaic_image_paddle.py:745 - 使用YOLO模型进行检测: models/yolov8_license_plate.pt
[2025-07-31 09:14:29] [    INFO] mosaic_image_paddle.py:763 - YOLO检测完成，共发现 0 个目标
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '15:08' (置信度: 0.980)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '5G 57' (置信度: 0.931)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '编辑' (置信度: 0.998)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '所有通话' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '未接来电' (置信度: 0.998)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '17184152754' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '17184152754' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '17184152754' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '昨天' (置信度: 0.997)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '浙江杭州' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:670 - 🔍 检测到敏感信息 (name): '浙江杭州' -> 匹配模式: [王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '浙江杭州' -> 类型: name
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '19103461464' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '19103461464' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '19103461464' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: ' i' (置信度: 0.746)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '山西太原' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:670 - 🔍 检测到敏感信息 (name): '山西太原' -> 匹配模式: [王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '山西太原' -> 类型: name
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '17196659851' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '17196659851' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '17196659851' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: 'i' (置信度: 0.844)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '浙江杭州' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:670 - 🔍 检测到敏感信息 (name): '浙江杭州' -> 匹配模式: [王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '浙江杭州' -> 类型: name
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '(021) 3876 0365' (置信度: 0.969)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:712 - 长数字串检测: '(021) 3876 0365' (长度: 15)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:723 - 未识别为敏感: '(021) 3876 0365' (可能是身份证号但未通过验证)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: 'i' (置信度: 0.997)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '上海' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '19157895079' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '19157895079' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '19157895079' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '浙江杭州' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:670 - 🔍 检测到敏感信息 (name): '浙江杭州' -> 匹配模式: [王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '浙江杭州' -> 类型: name
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '19143612412' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '19143612412' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '19143612412' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '浙江杭州' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:670 - 🔍 检测到敏感信息 (name): '浙江杭州' -> 匹配模式: [王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '浙江杭州' -> 类型: name
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '1' (置信度: 0.992)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '(0576) 9572 6' (置信度: 0.982)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: 'i' (置信度: 0.985)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '未知' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '13063509120' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '13063509120' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '13063509120' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: 'i' (置信度: 0.975)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '江苏徐州' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:670 - 🔍 检测到敏感信息 (name): '江苏徐州' -> 匹配模式: [王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '江苏徐州' -> 类型: name
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '17055813024' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '17055813024' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '17055813024' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '贵州贵阳' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '16222121915' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:662 - 🔍 检测到敏感信息 (mobile_phone): '16222121915' -> 匹配模式: \b1[3-9]\d{9}\b
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:718 - 敏感文字: '16222121915' -> 类型: mobile_phone
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '上海' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '1' (置信度: 0.995)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '(021) 3761 4890' (置信度: 0.970)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:712 - 长数字串检测: '(021) 3761 4890' (长度: 15)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:723 - 未识别为敏感: '(021) 3761 4890' (可能是身份证号但未通过验证)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '星期二' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: 'i' (置信度: 0.835)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '上海' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '滋' (置信度: 0.503)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '06' (置信度: 0.713)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '个人收藏' (置信度: 1.000)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '最近通话' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '通讯录' (置信度: 0.999)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '拨号键盘' (置信度: 0.986)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:708 - 识别文字: '语音留言' (置信度: 0.995)
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:727 - 发现 14 个敏感文字需要脱敏
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_name 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_name 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_name 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_name 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_name 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_name 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:832 - 脱敏成功: sensitive_text_mobile_phone 使用 mosaic 方法
[2025-07-31 09:14:37] [    INFO] mosaic_image_paddle.py:845 - 处理完成: 2.jpg (脱敏 14 个区域)
