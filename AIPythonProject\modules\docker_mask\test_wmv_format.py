#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WMV格式处理
"""

from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_wmv_format_handling():
    """测试WMV格式的输出路径处理"""
    
    print("🧪 测试WMV格式处理...")
    
    # 模拟输入和输出路径
    input_path = "E:\\testCase\\v_input\\id.wmv"
    output_path = "E:\\testCase\\v_output\\id.wmv"
    
    input_path_obj = Path(input_path)
    output_path_obj = Path(output_path)
    
    print(f"输入文件: {input_path}")
    print(f"输出路径: {output_path}")
    print(f"输入扩展名: {input_path_obj.suffix}")
    print(f"输出扩展名: {output_path_obj.suffix}")
    
    # 检查扩展名是否一致
    if input_path_obj.suffix.lower() == output_path_obj.suffix.lower():
        print("✅ 扩展名一致")
    else:
        print("❌ 扩展名不一致")
    
    # 测试格式映射
    input_ext = input_path_obj.suffix.lower()
    
    if input_ext == '.wmv':
        target_format = 'wmv'
        print(f"✅ WMV格式映射正确: {target_format}")
    else:
        print(f"❌ 格式映射错误")
    
    # 测试编解码器选择
    from mosaic_video_paddle import VideoProcessorPaddle
    
    processor = VideoProcessorPaddle(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=False,
        output_format='same'
    )
    
    codecs = processor._get_format_codecs('wmv')
    print(f"WMV格式可用编解码器: {codecs}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_wmv_format_handling()
