# 敏感信息脱敏工具使用说明

## 概述

本工具支持图片和视频的敏感信息脱敏处理，包括：
- **YOLO目标检测**：人脸、车牌等目标检测和马赛克处理
- **OCR文字检测**：身份证号、银行卡号、姓名等敏感文字检测和脱敏
- **多种脱敏方式**：马赛克、模糊、噪声、黑条等

## 支持的模型

### YOLO模型
- `yolov8n-face` - 人脸检测模型
- `yolov8_license_plate` - 车牌检测模型

### OCR模式
- `all` - 检测所有敏感信息
- `id_card` - 仅检测身份证相关信息
- `bank_card` - 仅检测银行卡相关信息

## 本地执行方式

### 环境要求
```bash
pip install -r requirements.txt
```

### 图片处理

#### 单个图片文件
```bash
# 仅YOLO人脸检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face

# 仅YOLO车牌检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8_license_plate

# 多个YOLO模型同时检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face -target yolov8_license_plate

# 仅OCR敏感文字检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg --enable_ocr --no_yolo

# YOLO + OCR组合检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face --enable_ocr

# 指定OCR模式和文字脱敏方式
python mosaic_image_paddle.py -i input.jpg -o output.jpg --enable_ocr --ocr_mode id_card --text_method blur --no_yolo
```

#### 批量处理图片文件夹
```bash
# 批量处理 - 仅YOLO检测
python mosaic_image_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face

# 批量处理 - 多模型检测
python mosaic_image_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face -target yolov8_license_plate

# 批量处理 - YOLO + OCR组合
python mosaic_image_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face --enable_ocr

# 批量处理 - 仅OCR检测
python mosaic_image_paddle.py -i input_folder -o output_folder --batch --enable_ocr --no_yolo
```

### 视频处理

#### 单个视频文件
```bash
# 仅YOLO人脸检测
python mosaic_video_paddle.py -i input.mp4 -o output.mp4 -target yolov8n-face

# 多个YOLO模型同时检测
python mosaic_video_paddle.py -i input.mp4 -o output.mp4 -target yolov8n-face -target yolov8_license_plate

# 仅OCR敏感文字检测
python mosaic_video_paddle.py -i input.mp4 -o output.mp4 --enable_ocr --no_yolo

# YOLO + OCR组合检测，指定OCR检测间隔
python mosaic_video_paddle.py -i input.mp4 -o output.mp4 -target yolov8n-face --enable_ocr --ocr_interval 10

# 指定输出格式
python mosaic_video_paddle.py -i input.avi -o output.mp4 -target yolov8n-face --output_format mp4

# 动态内容模式（高频OCR检测）
python mosaic_video_paddle.py -i input.mp4 -o output.mp4 --enable_ocr --dynamic_content --no_yolo
```

#### 批量处理视频文件夹
```bash
# 批量处理 - 仅YOLO检测
python mosaic_video_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face

# 批量处理 - 多模型检测
python mosaic_video_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face -target yolov8_license_plate

# 批量处理 - YOLO + OCR组合
python mosaic_video_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face --enable_ocr --ocr_interval 15
```

## Docker 执行方式

### 构建Docker镜像
```bash
docker build -t mosaic_tool:latest .
```

### 图片处理

#### 单个图片文件
```bash
# 仅YOLO人脸检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8n-face

# 仅YOLO车牌检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8_license_plate

# 多个YOLO模型同时检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg \
  -target yolov8n-face -target yolov8_license_plate

# 仅OCR敏感文字检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg \
  --enable-ocr --no-yolo

# YOLO + OCR组合检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg \
  -target yolov8n-face --enable-ocr

# 指定OCR模式
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg \
  --enable-ocr --ocr-mode id_card --no-yolo
```

#### 批量处理图片文件夹
```bash
# 批量处理 - 仅YOLO检测
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type image -input /app/input -output /app/output --batch -target yolov8n-face

# 批量处理 - 多模型检测
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type image -input /app/input -output /app/output --batch \
  -target yolov8n-face -target yolov8_license_plate

# 批量处理 - YOLO + OCR组合
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type image -input /app/input -output /app/output --batch \
  -target yolov8n-face --enable-ocr
```

### 视频处理

#### 单个视频文件
```bash
# 仅YOLO人脸检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type video -input /app/data/input.mp4 -output /app/data/output.mp4 -target yolov8n-face

# 多个YOLO模型同时检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type video -input /app/data/input.mp4 -output /app/data/output.mp4 \
  -target yolov8n-face -target yolov8_license_plate

# 仅OCR敏感文字检测
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type video -input /app/data/input.mp4 -output /app/data/output.mp4 \
  --enable-ocr --no-yolo

# YOLO + OCR组合检测，指定OCR检测间隔
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type video -input /app/data/input.mp4 -output /app/data/output.mp4 \
  -target yolov8n-face --enable-ocr --ocr-interval 10

# 指定输出格式
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type video -input /app/data/input.avi -output /app/data/output.mp4 \
  -target yolov8n-face --output-format mp4
```

#### 批量处理视频文件夹
```bash
# 批量处理 - 仅YOLO检测
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type video -input /app/input -output /app/output --batch -target yolov8n-face

# 批量处理 - 多模型检测
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type video -input /app/input -output /app/output --batch \
  -target yolov8n-face -target yolov8_license_plate

# 批量处理 - YOLO + OCR组合
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type video -input /app/input -output /app/output --batch \
  -target yolov8n-face --enable-ocr --ocr-interval 15
```

## 参数说明

### 必需参数
- `-type <类型>` - 处理类型：`image` 或 `video`
- `-input <路径>` - 输入文件或目录路径
- `-output <路径>` - 输出文件或目录路径

### 可选参数
- `-target <模型>` - YOLO模型名称，可多次使用
- `--enable-ocr` - 启用OCR检测
- `--ocr-mode <模式>` - OCR模式：`all`, `id_card`, `bank_card`
- `--ocr-interval <数值>` - OCR检测帧间隔（视频专用，默认15）
- `--output-format <格式>` - 输出格式：`mp4`, `avi`, `mov`, `mkv`, `same`, `auto`
- `--batch` - 批量处理模式
- `--no-yolo` - 禁用YOLO检测

## 注意事项

1. **路径映射**：Docker使用时注意正确映射主机路径到容器路径
2. **模型文件**：确保YOLO模型文件存在于`models/`目录下
3. **内存使用**：多模型同时使用会增加内存消耗
4. **处理速度**：OCR检测间隔越小，检测越准确但速度越慢
5. **输出格式**：视频处理时建议使用`mp4`格式以获得最佳兼容性

## 常见问题

### Q: 如何同时使用多个YOLO模型？
A: 使用多个`-target`参数：`-target yolov8n-face -target yolov8_license_plate`

### Q: 如何提高视频处理速度？
A: 增加`--ocr-interval`值，减少OCR检测频率

### Q: 如何只进行文字脱敏？
A: 使用`--enable-ocr --no-yolo`参数组合

### Q: Docker容器无法访问文件？
A: 检查路径映射是否正确，确保使用绝对路径

## 高级用法示例

### 复杂场景处理

#### 证件照片处理（身份证、银行卡等）
```bash
# 本地执行 - 专门处理身份证
python mosaic_image_paddle.py -i id_card.jpg -o id_card_masked.jpg \
  --enable_ocr --ocr_mode id_card --text_method blur --no_yolo

# Docker执行 - 专门处理银行卡
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/bank_card.jpg -output /app/data/bank_card_masked.jpg \
  --enable-ocr --ocr-mode bank_card --no-yolo
```

#### 监控视频处理
```bash
# 本地执行 - 监控视频人脸和车牌同时检测
python mosaic_video_paddle.py -i surveillance.mp4 -o surveillance_masked.mp4 \
  -target yolov8n-face -target yolov8_license_plate --ocr_interval 30

# Docker执行 - 高频OCR检测动态内容
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type video -input /app/data/dynamic_content.mp4 -output /app/data/output.mp4 \
  -target yolov8n-face --enable-ocr --ocr-interval 5
```

### 性能优化建议

#### 根据内容类型选择参数
```bash
# 静态内容（如文档扫描）- 低频OCR检测
--ocr-interval 30

# 一般视频内容 - 中频OCR检测
--ocr-interval 15

# 动态内容（如直播、快速变化场景）- 高频OCR检测
--ocr-interval 5

# 极高要求场景 - 每帧检测
--ocr-interval 1
```

#### 批量处理优化
```bash
# 大批量文件处理时，建议分批执行
# 第一批：人脸检测
docker run --rm -v /host/batch1:/app/input -v /host/output1:/app/output mosaic_tool:latest \
  -type image -input /app/input -output /app/output --batch -target yolov8n-face

# 第二批：车牌检测
docker run --rm -v /host/batch2:/app/input -v /host/output2:/app/output mosaic_tool:latest \
  -type image -input /app/input -output /app/output --batch -target yolov8_license_plate
```

## 文件结构说明

```
docker_mask/
├── models/                          # 模型文件目录
│   ├── yolov8n-face.pt             # 人脸检测模型
│   ├── yolov8_license_plate.pt     # 车牌检测模型
│   ├── paddleocr/                  # PaddleOCR模型目录
│   └── easyocr/                    # EasyOCR模型目录
├── mosaic_image_paddle.py          # 图片处理脚本
├── mosaic_video_paddle.py          # 视频处理脚本
├── run.sh                          # Docker入口脚本
├── Dockerfile                      # Docker构建文件
├── requirements.txt                # Python依赖
└── 使用说明.md                     # 本文档
```

## 输出示例

### 成功处理输出
```
处理: /app/input/test.jpg -> /app/output/test_masked.jpg (image)
[2025-07-30 17:37:03] [INFO] 根据target参数构建模型路径: ['models/yolov8n-face.pt', 'models/yolov8_license_plate.pt']
PaddleOCR 脱敏工具启动
输入: test.jpg
输出: test_masked.jpg
YOLO模型: yolov8n-face, yolov8_license_plate
OCR模式: all
🎯 YOLO检测到目标: face (置信度: 0.8542, 位置: (120, 80, 150, 180))
🎯 YOLO检测到目标: license_plate (置信度: 0.9123, 位置: (300, 400, 200, 60))
📝 OCR检测到敏感文字: 身份证号 (位置: (50, 300, 180, 25))
✅ 处理完成，共处理 3 个目标
处理完成: /app/output/test_masked.jpg
```

### 错误处理示例
```bash
# 模型文件不存在
错误: YOLO 模型文件不存在: models/invalid_model.pt

# 输入文件不存在
错误: 输入图片不存在: /path/to/nonexistent.jpg

# 参数缺失
错误: 缺少必要参数 -type -input -output
```

## 版本信息

- **Python**: 3.10+
- **PaddleOCR**: 2.7.3
- **Ultralytics**: 最新版本
- **OpenCV**: 4.x
- **Docker**: 支持多阶段构建

## 更新日志

### v2.0 (当前版本)
- ✅ 支持多个YOLO模型同时使用
- ✅ 统一图片和视频处理参数接口
- ✅ 优化Docker脚本参数解析
- ✅ 增强错误处理和日志输出

### v1.0
- ✅ 基础YOLO目标检测功能
- ✅ PaddleOCR文字检测功能
- ✅ Docker容器化支持
