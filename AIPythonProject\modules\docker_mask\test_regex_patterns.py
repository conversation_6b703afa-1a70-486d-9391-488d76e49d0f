#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正则表达式模式匹配
"""

import re

def test_landline_patterns():
    """测试固定电话正则表达式模式"""
    
    # 固定电话正则表达式模式
    landline_patterns = [
        # 标准固定电话：区号-号码（3-4位区号 + 7-8位号码）
        r'\b0\d{2,3}[\s\-]?\d{7,8}\b',
        # 带括号的区号格式 - 完整号码
        r'\(\d{3,4}\)[\s\-]?\d{7,8}\b',
        # 带括号的区号格式 - 分段号码（如 (021) 3761 4890）
        r'\(\d{3,4}\)[\s\-]?\d{4}[\s\-]?\d{4}\b',
        # 带括号的区号格式 - 更宽松的分段匹配（如 (0576) 9572 6）
        r'\(\d{3,4}\)[\s\-]?\d{4}[\s\-]?\d{1,4}\b',
        # 不带括号但有空格分隔的格式（如 021 3761 4890）
        r'\b0\d{2,3}[\s\-]\d{4}[\s\-]\d{4}\b',
        # 400/800客服电话
        r'\b[48]00[\s\-]?\d{3}[\s\-]?\d{4}\b',
        # 95开头的服务热线
        r'\b95\d{3,6}\b',
        # 10开头的特服号码
        r'\b10\d{3,6}\b',
        # 宽松模式：0开头的电话号码
        r'\b0\d{9,12}\b',
        # 超宽松模式：带括号的任意格式
        r'\(\d{3,4}\)[\s\-]*\d+[\s\-]*\d*',
    ]
    
    # 测试用例
    test_cases = [
        "(021) 3761 4890",
        "(0576) 9572 6",
        "(021) 3876 0365",
        "021-38764890",
        "0571-88888888",
        "021 3876 0365",
        "0576 9572 6",
        "400-123-4567",
        "95588",
        "10086"
    ]
    
    print("🧪 测试固定电话正则表达式模式匹配:")
    
    for test_text in test_cases:
        print(f"\n测试文本: '{test_text}'")
        matched = False
        
        for i, pattern in enumerate(landline_patterns):
            match = re.search(pattern, test_text)
            if match:
                print(f"  ✅ 模式 {i+1}: {pattern}")
                print(f"     匹配结果: '{match.group()}'")
                matched = True
        
        if not matched:
            print(f"  ❌ 没有模式匹配")

def test_sensitive_detection():
    """测试敏感信息检测"""
    from mosaic_image_paddle import ImageProcessorPaddle
    
    # 创建测试处理器
    processor = ImageProcessorPaddle(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=True,
        ocr_mode='phone',
        output_format='same'
    )
    
    test_cases = [
        "(021) 3761 4890",
        "(0576) 9572 6",
        "13812345678",
        "江苏徐州",
        "浙江杭州"
    ]
    
    print("\n\n🔍 测试敏感信息检测:")
    
    for text in test_cases:
        is_sensitive, info_type = processor.is_sensitive_text(text)
        status = "✅" if is_sensitive else "❌"
        print(f"  {status} '{text}' -> 敏感: {is_sensitive}, 类型: {info_type}")

if __name__ == "__main__":
    test_landline_patterns()
    test_sensitive_detection()
