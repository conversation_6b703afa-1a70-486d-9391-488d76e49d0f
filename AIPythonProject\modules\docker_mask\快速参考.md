# 敏感信息脱敏工具 - 快速参考

## 🚀 快速开始

### Docker方式（推荐）
```bash
# 构建镜像
docker build -t mosaic_tool:latest .

# 单文件处理
docker run --rm -v /host/data:/app/data mosaic_tool:latest \
  -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8n-face

# 文件夹批量处理
docker run --rm -v /host/input:/app/input -v /host/output:/app/output mosaic_tool:latest \
  -type image -input /app/input -output /app/output --batch -target yolov8n-face
```

### 本地方式
```bash
# 单文件处理
python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face

# 文件夹批量处理
python mosaic_image_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face
```

## 📋 常用命令模板

### 图片处理

| 场景 | Docker命令 | 本地命令 |
|------|------------|----------|
| 人脸检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8n-face` | `python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face` |
| 车牌检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8_license_plate` | `python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8_license_plate` |
| 多模型检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8n-face -target yolov8_license_plate` | `python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face -target yolov8_license_plate` |
| 仅OCR检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type image -input /app/data/input.jpg -output /app/data/output.jpg --enable-ocr --no-yolo` | `python mosaic_image_paddle.py -i input.jpg -o output.jpg --enable_ocr --no_yolo` |
| YOLO+OCR | `docker run --rm -v /data:/app/data mosaic_tool:latest -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8n-face --enable-ocr` | `python mosaic_image_paddle.py -i input.jpg -o output.jpg -target yolov8n-face --enable_ocr` |

### 视频处理

| 场景 | Docker命令 | 本地命令 |
|------|------------|----------|
| 人脸检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type video -input /app/data/input.mp4 -output /app/data/output.mp4 -target yolov8n-face` | `python mosaic_video_paddle.py -i input.mp4 -o output.mp4 -target yolov8n-face` |
| 多模型检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type video -input /app/data/input.mp4 -output /app/data/output.mp4 -target yolov8n-face -target yolov8_license_plate` | `python mosaic_video_paddle.py -i input.mp4 -o output.mp4 -target yolov8n-face -target yolov8_license_plate` |
| OCR高频检测 | `docker run --rm -v /data:/app/data mosaic_tool:latest -type video -input /app/data/input.mp4 -output /app/data/output.mp4 --enable-ocr --ocr-interval 5 --no-yolo` | `python mosaic_video_paddle.py -i input.mp4 -o output.mp4 --enable_ocr --ocr_interval 5 --no_yolo` |

### 批量处理

| 场景 | Docker命令 | 本地命令 |
|------|------------|----------|
| 图片批量 | `docker run --rm -v /input:/app/input -v /output:/app/output mosaic_tool:latest -type image -input /app/input -output /app/output --batch -target yolov8n-face` | `python mosaic_image_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face` |
| 视频批量 | `docker run --rm -v /input:/app/input -v /output:/app/output mosaic_tool:latest -type video -input /app/input -output /app/output --batch -target yolov8n-face` | `python mosaic_video_paddle.py -i input_folder -o output_folder --batch -target yolov8n-face` |

## ⚙️ 参数速查

### 必需参数
- `-type` : `image` 或 `video`
- `-input` : 输入路径
- `-output` : 输出路径

### YOLO相关
- `-target` : 模型名称（可多次使用）
  - `yolov8n-face` - 人脸检测
  - `yolov8_license_plate` - 车牌检测
- `--no-yolo` : 禁用YOLO检测

### OCR相关
- `--enable-ocr` : 启用OCR检测
- `--ocr-mode` : `all`, `id_card`, `bank_card`
- `--ocr-interval` : 检测间隔（视频专用，默认15）

### 其他
- `--batch` : 批量处理模式
- `--output-format` : 输出格式（视频专用）

## 🔧 性能调优

### OCR检测间隔建议
- **静态内容**: `--ocr-interval 30`
- **一般视频**: `--ocr-interval 15` 
- **动态内容**: `--ocr-interval 5`
- **高精度要求**: `--ocr-interval 1`

### 内存优化
- 单模型使用: 约2GB内存
- 双模型使用: 约3-4GB内存
- 大批量处理: 建议分批执行

## 🐛 常见问题

### 路径问题
```bash
# ❌ 错误：相对路径
docker run --rm -v ./data:/app/data ...

# ✅ 正确：绝对路径
docker run --rm -v /home/<USER>/data:/app/data ...
```

### 权限问题
```bash
# 如果输出文件权限有问题，添加用户映射
docker run --rm --user $(id -u):$(id -g) -v /data:/app/data ...
```

### 模型不存在
```bash
# 确保模型文件存在
ls models/yolov8n-face.pt
ls models/yolov8_license_plate.pt
```

## 📊 输出示例

### 成功输出
```
处理: /app/input/test.jpg -> /app/output/test.jpg (image)
[INFO] 根据target参数构建模型路径: ['models/yolov8n-face.pt']
🎯 YOLO检测到目标: face (置信度: 0.8542)
✅ 处理完成，共处理 1 个目标
处理完成: /app/output/test.jpg
```

### 错误输出
```
错误: 缺少必要参数 -type -input -output
错误: YOLO 模型文件不存在: models/invalid.pt
错误: 输入文件不存在: /path/to/file.jpg
```

## 🎯 最佳实践

1. **Docker使用绝对路径映射**
2. **批量处理时分批执行**
3. **根据内容类型调整OCR间隔**
4. **多模型使用时注意内存消耗**
5. **重要文件先小批量测试**

---

💡 **提示**: 更详细的说明请参考 `使用说明.md` 文档
