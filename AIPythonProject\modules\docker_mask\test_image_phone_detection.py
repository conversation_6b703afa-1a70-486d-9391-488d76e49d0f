#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片处理中的电话号码检测功能
"""

from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mosaic_image_paddle import ImageProcessor

def test_image_phone_detection():
    """测试图片处理中的电话号码检测功能"""
    
    # 创建测试处理器
    processor = ImageProcessor(
        yolo_model_paths=[],
        enable_yolo=False,
        enable_ocr=True,
        ocr_mode='phone'
    )
    
    print("🧪 测试图片处理中的电话号码检测功能...")
    
    # 手机号码测试用例
    mobile_test_cases = [
        # 有效手机号
        ("13812345678", True, "标准手机号"),
        ("138 1234 5678", True, "带空格的手机号"),
        ("138-1234-5678", True, "带连字符的手机号"),
        ("15987654321", True, "159号段"),
        ("18612345678", True, "186号段"),
        ("17012345678", True, "虚拟运营商号段"),
        
        # 无效手机号
        ("12812345678", False, "第二位不是3-9"),
        ("1381234567", False, "只有10位"),
        ("138123456789", False, "12位数字"),
        ("23812345678", False, "不是1开头"),
    ]
    
    # 固定电话测试用例
    landline_test_cases = [
        # 有效固话
        ("01012345678", True, "北京固话"),
        ("021-12345678", True, "上海固话带连字符"),
        ("0755 12345678", True, "深圳固话带空格"),
        ("************", True, "400客服电话"),
        ("************", True, "800客服电话"),
        ("95588", True, "银行服务热线"),
        ("10086", True, "移动客服"),
        
        # 无效固话
        ("12345678", False, "没有区号"),
        ("0123", False, "太短"),
        ("012345678901234", False, "太长"),
    ]
    
    print("\n📱 测试手机号码验证:")
    for phone, expected, description in mobile_test_cases:
        result = processor.validate_mobile_phone(phone)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {phone:<15} -> {result:<5} ({description})")
    
    print("\n☎️ 测试固定电话验证:")
    for phone, expected, description in landline_test_cases:
        result = processor.validate_landline_phone(phone)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {phone:<15} -> {result:<5} ({description})")
    
    print("\n🔍 测试敏感信息检测:")
    # 测试敏感信息检测
    text_test_cases = [
        ("联系电话13812345678", True, "mobile_phone"),
        ("请拨打010-12345678", True, "landline_phone"),
        ("客服热线：************", True, "landline_phone"),
        ("银行热线95588", True, "landline_phone"),
        ("这是普通文字", False, ""),
        ("电话号码12345", False, ""),
    ]
    
    for text, expected_sensitive, expected_type in text_test_cases:
        is_sensitive, info_type = processor.is_sensitive_text(text)
        status = "✅" if (is_sensitive == expected_sensitive and (not expected_sensitive or info_type == expected_type)) else "❌"
        print(f"  {status} '{text}' -> 敏感: {is_sensitive}, 类型: {info_type}")
    
    print(f"\n🎉 图片电话号码检测测试完成！")

if __name__ == "__main__":
    test_image_phone_detection()
